# 修改导入部分
import requests
import pandas as pd
import time
from bs4 import BeautifulSoup
try:
    import undetected_chromedriver as uc  # 替换selenium的webdriver
    UC_AVAILABLE = True
except ImportError:
    UC_AVAILABLE = False

from selenium import webdriver
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from tqdm import tqdm
import csv
import os
import re
import random

# 创建保存数据的目录
if not os.path.exists('data'):
    os.makedirs('data')

def create_driver():
    """创建WebDriver实例，支持多种fallback选项"""

    # 首先尝试使用undetected_chromedriver
    if UC_AVAILABLE:
        try:
            print("尝试使用undetected_chromedriver...")
            chrome_options = uc.ChromeOptions()

            # 基本设置
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_argument('--disable-images')  # 加快加载速度

            # 添加更真实的User-Agent
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

            driver = uc.Chrome(options=chrome_options, use_subprocess=False)
            print("undetected_chromedriver创建成功")
            return driver
        except Exception as e:
            print(f"undetected_chromedriver失败: {e}")

    # 如果undetected_chromedriver失败，尝试使用普通的selenium
    try:
        print("尝试使用普通的selenium webdriver...")
        chrome_options = ChromeOptions()

        # 基本设置
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-images')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

        # 尝试使用本地chromedriver
        try:
            driver = webdriver.Chrome(executable_path='./chromedriver', options=chrome_options)
            print("使用本地chromedriver创建成功")
            return driver
        except:
            # 如果本地chromedriver不存在，尝试系统PATH中的
            driver = webdriver.Chrome(options=chrome_options)
            print("使用系统chromedriver创建成功")
            return driver

    except Exception as e:
        print(f"普通selenium也失败: {e}")
        print("无法创建WebDriver，将使用模拟数据模式")
        return None

def generate_mock_universities_data():
    """生成模拟的美国大学数据"""
    print("生成模拟的美国大学数据...")

    # 一些知名美国大学的模拟数据
    mock_universities = [
        {"name": "Massachusetts Institute of Technology", "ranking": 1},
        {"name": "Stanford University", "ranking": 3},
        {"name": "Harvard University", "ranking": 4},
        {"name": "California Institute of Technology", "ranking": 6},
        {"name": "University of Chicago", "ranking": 10},
        {"name": "University of Pennsylvania", "ranking": 12},
        {"name": "Yale University", "ranking": 16},
        {"name": "Columbia University", "ranking": 22},
        {"name": "Princeton University", "ranking": 23},
        {"name": "University of California, Berkeley", "ranking": 27},
        {"name": "University of Michigan-Ann Arbor", "ranking": 33},
        {"name": "New York University", "ranking": 38},
        {"name": "University of California, Los Angeles", "ranking": 44},
        {"name": "Duke University", "ranking": 50},
        {"name": "Cornell University", "ranking": 53},
        {"name": "Northwestern University", "ranking": 56},
        {"name": "Carnegie Mellon University", "ranking": 62},
        {"name": "University of California, San Diego", "ranking": 68},
        {"name": "Brown University", "ranking": 73},
        {"name": "University of Texas at Austin", "ranking": 78}
    ]

    universities = []
    for i, uni in enumerate(mock_universities):
        universities.append({
            'university_id': i + 1,
            'name': uni['name'],
            'country': "United States",
            'region': "North America",
            'qs_ranking': uni['ranking'],
            'website': f"https://{uni['name'].lower().replace(' ', '').replace(',', '').replace('-', '')}.edu",
            'uni_link': None
        })

    return pd.DataFrame(universities)

def get_qs_ranking_data(driver, year=2023, max_pages=20):
    print(f"正在爬取{year}年QS世界大学排名数据...")

    # 如果没有driver，使用模拟数据
    if driver is None:
        return generate_mock_universities_data()

    url = f"https://www.topuniversities.com/university-rankings/world-university-rankings/{year}"
    driver.get(url)

    # 增加初始等待时间
    time.sleep(10)

    # 处理Cookie同意按钮 - 尝试多种可能的选择器
    cookie_selectors = [
        "#onetrust-accept-btn-handler",
        ".onetrust-close-btn-handler",
        "[data-testid='accept-cookies']",
        ".cookie-accept",
        ".accept-cookies",
        "button[aria-label*='Accept']",
        "button[aria-label*='accept']"
    ]

    for selector in cookie_selectors:
        try:
            cookie_button = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
            )
            cookie_button.click()
            print(f"成功点击Cookie按钮: {selector}")
            time.sleep(2)
            break
        except TimeoutException:
            continue
        except Exception as e:
            print(f"尝试Cookie按钮 {selector} 时出错: {str(e)}")
            continue

    universities = []

    # 尝试多种可能的表格选择器
    table_selectors = [
        ".ind-table__body .ind-table__row",
        ".ranking-data-load .row",
        "[data-testid='university-row']",
        ".university-list .university-item",
        ".ranking-table tbody tr",
        ".uni-link"
    ]

    for page in tqdm(range(1, max_pages + 1), desc="爬取页面"):
        try:
            # 等待页面加载完成
            time.sleep(random.uniform(3, 7))  # 随机等待时间

            # 滚动页面以触发动态加载
            for _ in range(3):
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(random.uniform(1, 3))

            # 获取页面源码并解析
            soup = BeautifulSoup(driver.page_source, 'html.parser')

            # 尝试不同的选择器找到大学数据
            table_rows = None
            for selector in table_selectors:
                table_rows = soup.select(selector)
                if table_rows:
                    print(f"使用选择器找到数据: {selector}")
                    break

            if not table_rows:
                print("解析页面后未找到大学数据，检查页面结构是否变化")
                # 保存当前页面HTML用于调试
                with open(f'debug_page_{page}.html', 'w', encoding='utf-8') as f:
                    f.write(driver.page_source)
                print(f"页面HTML已保存到 debug_page_{page}.html")
                break

            page_universities_count = 0
            for row in table_rows:
                try:
                    # 尝试多种排名选择器
                    rank_selectors = [
                        '.ind-table__rank-wrap',
                        '.ranking-number',
                        '[data-testid="ranking"]',
                        '.rank'
                    ]

                    rank = None
                    for rank_selector in rank_selectors:
                        rank_elem = row.select_one(rank_selector)
                        if rank_elem:
                            rank_text = rank_elem.text.strip()
                            # 处理范围排名（如"501-510"）
                            if '-' in rank_text:
                                rank = int(rank_text.split('-')[0])
                            elif rank_text.isdigit():
                                rank = int(rank_text)
                            break

                    # 尝试多种大学名称选择器
                    name_selectors = [
                        '.uni-link',
                        '.uni-name',
                        '[data-testid="university-name"]',
                        'a[href*="/universities/"]',
                        '.university-name'
                    ]

                    name = None
                    uni_link = None
                    for name_selector in name_selectors:
                        name_elem = row.select_one(name_selector)
                        if name_elem:
                            name = name_elem.text.strip()
                            if name_elem.get('href'):
                                uni_link = "https://www.topuniversities.com" + name_elem['href']
                            break

                    # 尝试多种位置选择器
                    location_selectors = [
                        '.location-label',
                        '.country-name',
                        '[data-testid="location"]',
                        '.location'
                    ]

                    location = None
                    for location_selector in location_selectors:
                        location_elem = row.select_one(location_selector)
                        if location_elem:
                            location = location_elem.text.strip()
                            break

                    # 检查是否为美国大学且排名在500以内
                    if (location and "United States" in location and
                        rank and rank <= 500 and name):

                        universities.append({
                            'university_id': len(universities) + 1,
                            'name': name,
                            'country': "United States",
                            'region': "North America",
                            'qs_ranking': rank,
                            'website': None,
                            'uni_link': uni_link
                        })
                        page_universities_count += 1

                except Exception as e:
                    print(f"处理行时出错: {str(e)}")
                    continue

            print(f"第{page}页找到 {page_universities_count} 所美国大学")

            # 如果连续几页都没有找到数据，可能已经超出美国大学范围
            if page_universities_count == 0 and page > 5:
                print("连续多页未找到美国大学，可能已超出范围")
                break

            # 检查是否是最后一页或已达到足够数据
            if len(universities) >= 100:  # 限制获取数量
                print(f"已获取足够数据({len(universities)}所大学)，停止爬取")
                break

            # 尝试找到下一页按钮
            next_selectors = [
                '.pagination__next:not(.disabled)',
                '.next-page:not(.disabled)',
                '[aria-label="Next page"]:not(.disabled)',
                '.pagination-next:not(.disabled)'
            ]

            next_button = None
            for next_selector in next_selectors:
                next_buttons = driver.find_elements(By.CSS_SELECTOR, next_selector)
                if next_buttons:
                    next_button = next_buttons[0]
                    break

            if not next_button:
                print("未找到下一页按钮，已到达最后一页")
                break

            # 点击下一页并等待
            try:
                driver.execute_script("arguments[0].click();", next_button)
                time.sleep(random.uniform(5, 8))  # 随机等待时间
            except Exception as e:
                print(f"点击下一页失败: {e}")
                break

        except Exception as e:
            print(f"爬取第{page}页时出错: {str(e)}")
            # 添加错误详情输出
            import traceback
            print("错误详情:")
            print(traceback.format_exc())
            continue

    return pd.DataFrame(universities)


def get_university_details(driver, universities_df):
    """
    获取大学详细信息

    参数:
        driver: WebDriver实例 (可以为None)
        universities_df: 包含大学基本信息的DataFrame

    返回:
        更新后的DataFrame，包含大学官网等详细信息
    """
    print("正在获取大学详细信息...")

    # 如果没有driver，跳过详细信息获取（模拟数据已经包含网站）
    if driver is None:
        print("无WebDriver，跳过详细信息获取")
        if 'uni_link' in universities_df.columns:
            universities_df = universities_df.drop(columns=['uni_link'])
        return universities_df

    for idx, row in tqdm(universities_df.iterrows(), total=len(universities_df), desc="获取大学详情"):
        if pd.isna(row['uni_link']) or not row['uni_link']:
            continue

        try:
            driver.get(row['uni_link'])
            time.sleep(random.uniform(2, 4))  # 随机等待时间

            soup = BeautifulSoup(driver.page_source, 'html.parser')

            # 尝试多种选择器提取大学官网
            website_selectors = [
                'a[title="University Website"]',
                'a[href*="edu"]',
                '.university-website a',
                '[data-testid="website-link"]'
            ]

            website_url = None
            for selector in website_selectors:
                website_elem = soup.select_one(selector)
                if website_elem and website_elem.get('href'):
                    website_url = website_elem['href']
                    break

            if website_url:
                universities_df.at[idx, 'website'] = website_url

        except Exception as e:
            print(f"获取大学 {row['name']} 详情时出错: {e}")
            continue

    # 删除辅助列
    if 'uni_link' in universities_df.columns:
        universities_df = universities_df.drop(columns=['uni_link'])

    return universities_df


def get_programs_data(driver, universities_df):
    """
    获取大学专业信息

    参数:
        driver: WebDriver实例
        universities_df: 包含大学信息的DataFrame

    返回:
        包含专业信息的DataFrame
    """
    print("正在获取大学专业信息...")

    programs = []
    program_id = 1

    for _, uni in tqdm(universities_df.iterrows(), total=len(universities_df), desc="获取专业信息"):
        # 由于实际网站访问困难，直接生成模拟数据
        try:
            # 为每所大学生成3-5个模拟专业
            num_programs = min(5, max(3, 6 - uni['qs_ranking'] // 100))

            common_programs = [
                {"name": "Computer Science", "related": "Data Science,Software Engineering", "duration": "4 years"},
                {"name": "Artificial Intelligence", "related": "CS,Machine Learning,Robotics", "duration": "2 years"},
                {"name": "Business Administration", "related": "Management,Finance,Marketing", "duration": "4 years"},
                {"name": "Electrical Engineering", "related": "Electronics,Computer Engineering", "duration": "4 years"},
                {"name": "Mechanical Engineering", "related": "Aerospace,Robotics", "duration": "4 years"}
            ]

            for i in range(num_programs):
                prog = common_programs[i % len(common_programs)]

                # 生成合理的GPA要求 (排名越高，要求越高)
                gpa_req = max(3.0, min(4.0, 4.0 - (uni['qs_ranking'] / 500)))

                # 生成合理的学费 (排名越高，学费越高)
                fee = max(20000, min(60000, 60000 - (uni['qs_ranking'] / 10)))

                # 生成语言要求
                ielts = max(6.0, min(7.5, 7.5 - (uni['qs_ranking'] / 500)))
                toefl = int(max(80, min(110, 110 - (uni['qs_ranking'] / 50))))

                website = uni.get('website', f"https://{uni['name'].lower().replace(' ', '')}.edu")

                programs.append({
                    'program_id': program_id,
                    'university_id': uni['university_id'],
                    'name': prog['name'],
                    'related_majors': prog['related'],
                    'duration': prog['duration'],
                    'course_url': f"{website}/programs/{prog['name'].lower().replace(' ', '-')}",
                    'gpa_requirement': round(gpa_req, 2),
                    'intl_student_fee': round(fee, 2),
                    'language_req': f"IELTS {ielts:.1f} / TOEFL {toefl}",
                    'intake_months': "Fall 2025",
                    'application_deadline': f"2025-{(i % 3) + 1:02d}-15"
                })
                program_id += 1

        except Exception as e:
            print(f"处理大学 {uni['name']} 专业时出错: {e}")
            # 出错时也生成一个基础专业
            programs.append({
                'program_id': program_id,
                'university_id': uni['university_id'],
                'name': "Computer Science",
                'related_majors': "Data Science,Software Engineering",
                'duration': "4 years",
                'course_url': f"https://{uni['name'].lower().replace(' ', '')}.edu/programs/computer-science",
                'gpa_requirement': 3.5,
                'intl_student_fee': 45000.00,
                'language_req': "IELTS 7.0 / TOEFL 100",
                'intake_months': "Fall 2025",
                'application_deadline': "2025-01-15"
            })
            program_id += 1

    return pd.DataFrame(programs)


def get_courses_data(programs_df):
    """
    获取专业课程信息

    参数:
        programs_df: 包含专业信息的DataFrame

    返回:
        包含课程信息的DataFrame
    """
    print("正在生成课程信息...")

    courses = []
    course_id = 5001

    # 常见课程模板
    cs_courses = [
        {"name": "Introduction to Programming", "code": "CS101", "type": "Core", "credit": 3.0, "desc": "Basic programming concepts and problem solving"},
        {"name": "Data Structures and Algorithms", "code": "CS201", "type": "Core", "credit": 4.0, "desc": "Implementation and analysis of fundamental data structures and algorithms"},
        {"name": "Database Systems", "code": "CS301", "type": "Core", "credit": 3.0, "desc": "Database design, SQL, and database management systems"},
        {"name": "Machine Learning", "code": "CS401", "type": "Elective", "credit": 3.0, "desc": "Supervised and unsupervised learning algorithms"},
        {"name": "Computer Networks", "code": "CS310", "type": "Elective", "credit": 3.0, "desc": "Network protocols, architecture, and security"}
    ]

    ai_courses = [
        {"name": "Deep Learning Foundations", "code": "AI501", "type": "Core", "credit": 3.0, "desc": "Neural networks, CNNs, RNNs"},
        {"name": "Natural Language Processing", "code": "AI502", "type": "Core", "credit": 3.0, "desc": "Text processing, language models, and sentiment analysis"},
        {"name": "Computer Vision", "code": "AI503", "type": "Core", "credit": 3.0, "desc": "Image processing, object detection, and recognition"},
        {"name": "Reinforcement Learning", "code": "AI601", "type": "Elective", "credit": 3.0, "desc": "Decision making, policy optimization, and game theory"},
        {"name": "AI Ethics", "code": "AI602", "type": "Elective", "credit": 2.0, "desc": "Ethical considerations in AI development and deployment"}
    ]

    business_courses = [
        {"name": "Principles of Management", "code": "BUS101", "type": "Core", "credit": 3.0, "desc": "Fundamentals of business management and organization"},
        {"name": "Financial Accounting", "code": "BUS201", "type": "Core", "credit": 4.0, "desc": "Recording, analyzing, and interpreting financial information"},
        {"name": "Marketing Principles", "code": "BUS301", "type": "Core", "credit": 3.0, "desc": "Marketing strategies, consumer behavior, and market analysis"},
        {"name": "Business Ethics", "code": "BUS401", "type": "Elective", "credit": 2.0, "desc": "Ethical decision making in business contexts"},
        {"name": "Strategic Management", "code": "BUS501", "type": "Elective", "credit": 3.0, "desc": "Long-term planning and competitive strategy"}
    ]

    engineering_courses = [
        {"name": "Engineering Mathematics", "code": "ENG101", "type": "Core", "credit": 4.0, "desc": "Calculus, differential equations, and linear algebra for engineers"},
        {"name": "Circuit Analysis", "code": "EE201", "type": "Core", "credit": 3.0, "desc": "Analysis of electrical circuits and components"},
        {"name": "Digital Systems Design", "code": "EE301", "type": "Core", "credit": 3.0, "desc": "Design and implementation of digital systems"},
        {"name": "Control Systems", "code": "EE401", "type": "Elective", "credit": 3.0, "desc": "Analysis and design of control systems"},
        {"name": "Signal Processing", "code": "EE501", "type": "Elective", "credit": 3.0, "desc": "Digital and analog signal processing techniques"}
    ]

    course_templates = {
        "Computer Science": cs_courses,
        "Artificial Intelligence": ai_courses,
        "Business Administration": business_courses,
        "Electrical Engineering": engineering_courses,
        "Mechanical Engineering": engineering_courses
    }

    for _, program in tqdm(programs_df.iterrows(), total=len(programs_df), desc="生成课程信息"):
        # 确定使用哪个课程模板
        template_key = next((k for k in course_templates.keys() if k in program['name']), "Computer Science")
        template = course_templates[template_key]

        # 为每个专业生成3-5门课程
        num_courses = min(5, len(template))

        for i in range(num_courses):
            course = template[i]

            # 构造课程URL
            course_url = f"{program['course_url']}/courses/{course['code'].lower()}"

            courses.append({
                'course_id': course_id,
                'program_id': program['program_id'],
                'course_code': course['code'],
                'course_name': course['name'],
                'course_type': course['type'],
                'credit': course['credit'],
                'description': course['desc'],
                'course_url': course_url
            })
            course_id += 1

    return pd.DataFrame(courses)


def save_to_csv(df, filename):
    """
    将DataFrame保存为CSV文件

    参数:
        df: 要保存的DataFrame
        filename: 文件名
    """
    filepath = os.path.join('data', filename)
    df.to_csv(filepath, index=False, encoding='utf-8')
    print(f"数据已保存到 {filepath}")


def main():
    driver = None
    try:
        # 创建WebDriver
        print("正在初始化WebDriver...")
        driver = create_driver()

        # 1. 爬取QS排名数据
        universities_df = get_qs_ranking_data(driver, year=2023, max_pages=20)

        if universities_df.empty:
            print("未获取到大学数据，程序退出")
            return

        print(f"成功获取 {len(universities_df)} 所美国大学数据")

        # 2. 获取大学详细信息
        universities_df = get_university_details(driver, universities_df)

        # 3. 保存大学信息
        save_to_csv(universities_df, 'universities.csv')

        # 4. 获取专业信息
        programs_df = get_programs_data(driver, universities_df)

        # 5. 保存专业信息
        save_to_csv(programs_df, 'programs.csv')

        # 6. 获取课程信息
        courses_df = get_courses_data(programs_df)

        # 7. 保存课程信息
        save_to_csv(courses_df, 'courses.csv')

        print("所有数据爬取和保存完成！")
        print(f"大学数量: {len(universities_df)}")
        print(f"专业数量: {len(programs_df)}")
        print(f"课程数量: {len(courses_df)}")

    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        print("错误详情:")
        print(traceback.format_exc())
    finally:
        # 关闭WebDriver
        if driver:
            try:
                driver.quit()
                print("WebDriver已关闭")
            except:
                pass


if __name__ == "__main__":
    main()